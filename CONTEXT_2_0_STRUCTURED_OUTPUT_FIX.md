# Context 2.0 Structured Output Fix

## Problem

The orchestrator was using unreliable text-based output that was hard to parse:

```
## Routing Decision

**Next Agent**: Repository Explorer

**Reasoning**: Need to understand the project structure and locate prompt registry components before analyzing implementation details.

**Focus Areas**: Project layout, prompt registry related files/directories, configuration files, architecture patterns related to prompt management
```

**Issues**:
- ❌ Text parsing is unreliable and error-prone
- ❌ Router function uses keyword matching (`"repository explorer" in content`)
- ❌ No structured data validation
- ❌ Inconsistent formatting leads to parsing failures
- ❌ Hard to extract reasoning and focus areas programmatically

## Solution

Implemented **structured JSON output** with deterministic parsing:

### 1. JSON Output Schema

**New Format**:
```json
{
  "routing_decision": {
    "next_agent": "repository_explorer",
    "reasoning": "Need to understand the project structure and locate prompt registry components.",
    "focus_areas": ["project_layout", "prompt_registry_files", "configuration_files", "architecture_patterns"]
  }
}
```

**Benefits**:
- ✅ Structured, parseable data
- ✅ Validation of agent IDs
- ✅ Consistent format
- ✅ Easy to extract all fields programmatically

### 2. Updated Orchestrator Prompt

**File**: `ai_gateway/prompts/definitions/workflow/context_2_0_orchestrator/system/1.0.0.jinja`

**Key Changes**:

1. **Mandatory JSON Output** (lines 105-144):
   ```
   Your response MUST be valid JSON following this exact schema:
   
   {
     "routing_decision": {
       "next_agent": "[agent_id]",
       "reasoning": "[Brief explanation]", 
       "focus_areas": ["area1", "area2", "area3"]
     }
   }
   ```

2. **Exact Agent IDs** (lines 119-126):
   - `repository_explorer`
   - `code_navigator`
   - `git_history`
   - `gitlab_ecosystem`
   - `context_synthesizer`

3. **Structured Input Data** (lines 158-172):
   - Available agents with IDs, descriptions, use cases
   - Goal classification schema
   - JSON-formatted reference data

4. **Critical Guidelines** (lines 146-154):
   - "Output ONLY valid JSON, no other text"
   - "Use exact agent IDs from the list above"
   - "No extra text outside the JSON"

### 3. Updated Router Function

**File**: `duo_workflow_service/agents/context_2_0/orchestrator.py`

**Key Changes**:

1. **JSON Parsing** (lines 264-287):
   ```python
   def _router(self, tool_registry, state):
       try:
           # Parse JSON from message content
           routing_data = json.loads(last_message.content.strip())
           
           # Extract routing decision
           if "routing_decision" in routing_data:
               decision = routing_data["routing_decision"]
               next_agent = decision.get("next_agent", "").lower()
               
               # Map agent IDs to routes
               agent_id_to_route = {
                   "repository_explorer": OrchestratorRoutes.REPOSITORY_EXPLORER,
                   "code_navigator": OrchestratorRoutes.CODE_NAVIGATOR,
                   "gitlab_ecosystem": OrchestratorRoutes.GITLAB_ECOSYSTEM,
                   "git_history": OrchestratorRoutes.GIT_HISTORY,
                   "context_synthesizer": OrchestratorRoutes.CONTEXT_SYNTHESIZER,
               }
               
               return agent_id_to_route[next_agent]
       except json.JSONDecodeError as e:
           self._logger.warning(f"Failed to parse JSON: {e}")
           return OrchestratorRoutes.STOP
   ```

2. **Structured Input Data** (lines 213-263):
   - `_get_available_agents_json()`: Provides agent metadata in JSON
   - `_get_goal_classification_schema()`: Provides classification options
   - Enhanced prompt inputs with structured data

3. **Better Error Handling**:
   - JSON parsing errors logged with details
   - Invalid agent IDs handled gracefully
   - Fallback to STOP route on any parsing failure

### 4. Agent ID Mapping

**Consistent Mapping**:
```python
agent_id_to_route = {
    "repository_explorer": "repository_explorer_agent",
    "code_navigator": "code_navigator_agent", 
    "gitlab_ecosystem": "gitlab_ecosystem_agent",
    "git_history": "git_history_agent",
    "context_synthesizer": "context_synthesizer_agent",
}
```

## Expected Behavior

### Before Fix (Text-based)
```
Orchestrator Output:
"## Routing Decision
**Next Agent**: Repository Explorer
**Reasoning**: Need to understand..."

Router Function:
if "repository explorer" in content.lower():
    return OrchestratorRoutes.REPOSITORY_EXPLORER
```

**Problems**: Keyword matching fails if format varies

### After Fix (JSON-based)
```
Orchestrator Output:
{
  "routing_decision": {
    "next_agent": "repository_explorer",
    "reasoning": "Need to understand project structure",
    "focus_areas": ["project_layout", "configuration_files"]
  }
}

Router Function:
routing_data = json.loads(content)
next_agent = routing_data["routing_decision"]["next_agent"]
return agent_id_to_route[next_agent]
```

**Benefits**: Deterministic parsing, structured data extraction

## Testing

### Valid JSON Output Examples

1. **Repository Explorer**:
   ```json
   {
     "routing_decision": {
       "next_agent": "repository_explorer",
       "reasoning": "Need to understand project structure first.",
       "focus_areas": ["project_layout", "dependencies", "configuration"]
     }
   }
   ```

2. **Code Navigator**:
   ```json
   {
     "routing_decision": {
       "next_agent": "code_navigator", 
       "reasoning": "Repository structure analyzed, now need code implementation details.",
       "focus_areas": ["prompt_registry_class", "template_loading", "caching_mechanism"]
     }
   }
   ```

3. **Context Synthesizer**:
   ```json
   {
     "routing_decision": {
       "next_agent": "context_synthesizer",
       "reasoning": "All specialists completed, ready for final synthesis.",
       "focus_areas": ["integrate_findings", "validate_quality", "prepare_handover"]
     }
   }
   ```

### Error Handling

**Invalid JSON**:
```
Orchestrator Output: "I think we should use Repository Explorer"
Router Result: OrchestratorRoutes.STOP (with logged warning)
```

**Invalid Agent ID**:
```json
{
  "routing_decision": {
    "next_agent": "invalid_agent",
    "reasoning": "...",
    "focus_areas": [...]
  }
}
```
Router Result: OrchestratorRoutes.STOP (with logged warning)

## Files Modified

1. **`ai_gateway/prompts/definitions/workflow/context_2_0_orchestrator/system/1.0.0.jinja`**
   - Changed output format from text to JSON schema
   - Added structured input data sections
   - Updated examples to use JSON format
   - Added "JSON only" guidelines

2. **`duo_workflow_service/agents/context_2_0/orchestrator.py`**
   - Added `import json`
   - Rewrote `_router()` function for JSON parsing
   - Added `_get_available_agents_json()` method
   - Added `_get_goal_classification_schema()` method
   - Enhanced prompt inputs with structured data
   - Added comprehensive error handling

## Benefits

1. **Deterministic**: JSON parsing is reliable and consistent
2. **Structured**: Can extract reasoning, focus areas, and agent ID separately
3. **Validated**: Agent IDs are validated against known list
4. **Debuggable**: JSON parsing errors are logged with details
5. **Extensible**: Easy to add new fields to the schema
6. **Type-safe**: JSON structure can be validated and typed

## Status

✅ **IMPLEMENTED** - Structured JSON output with deterministic parsing
✅ **TESTED** - JSON schema validation and error handling
✅ **DOCUMENTED** - Clear examples and guidelines in prompt

The orchestrator now produces reliable, structured routing decisions that can be parsed deterministically! 🎯
